{"name": "aura-interface", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@ant-design/x": "^1.0.5", "@react-router/node": "^7.2.0", "@react-router/serve": "^7.2.0", "antd": "^5.24.1", "axios": "^1.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.2.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@react-router/dev": "^7.2.0", "@tailwindcss/vite": "^4.0.0", "@types/node": "^22.13.5", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "react-router-devtools": "^1.1.5", "tailwindcss": "^4.0.0", "typescript": "^5.7.3", "vite": "^6.1.0"}}